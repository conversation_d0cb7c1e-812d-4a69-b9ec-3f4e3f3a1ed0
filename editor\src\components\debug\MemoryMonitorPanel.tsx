/**
 * 内存监控面板组件
 * 提供内存使用监控和泄漏检测功能
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Tabs,
  List,
  Modal,
  Alert,
  Badge,
  Descriptions
} from 'antd';
import {
  MoreOutlined,
  DeleteOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  LineChartOutlined,
  BugOutlined,
  ClearOutlined,
  CompressOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { Area } from '@ant-design/charts';
import { useTranslation } from 'react-i18next';
import MemoryMonitorService, {
  MemorySnapshot,
  MemoryLeakDetection,
  LeakType,
  LeakSeverity
} from '../../services/MemoryMonitorService';
import './MemoryMonitorPanel.less';

const { Text } = Typography;
const { TabPane } = Tabs;

interface MemoryMonitorPanelProps {
  visible: boolean;
  onClose: () => void;
}

const MemoryMonitorPanel: React.FC<MemoryMonitorPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [snapshots, setSnapshots] = useState<MemorySnapshot[]>([]);
  const [leakDetections, setLeakDetections] = useState<MemoryLeakDetection[]>([]);
  const [memoryStats, setMemoryStats] = useState<any>({});
  const [memoryTrend, setMemoryTrend] = useState<any>({});
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSnapshots, setSelectedSnapshots] = useState<string[]>([]);

  const [selectedLeak, setSelectedLeak] = useState<MemoryLeakDetection | null>(null);
  const [leakDetailVisible, setLeakDetailVisible] = useState(false);

  const memoryService = MemoryMonitorService.getInstance();
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, [visible]);

  const setupEventListeners = () => {
    memoryService.on('snapshotCreated', handleSnapshotCreated);
    memoryService.on('memoryLeakDetected', handleLeakDetected);
    
    // 定期更新数据
    updateIntervalRef.current = setInterval(() => {
      updateData();
    }, 2000);
  };

  const cleanupEventListeners = () => {
    memoryService.off('snapshotCreated', handleSnapshotCreated);
    memoryService.off('memoryLeakDetected', handleLeakDetected);
  };

  const loadData = () => {
    setSnapshots(memoryService.getSnapshots());
    setLeakDetections(memoryService.getLeakDetections());
    setMemoryStats(memoryService.getMemoryStats());
    setMemoryTrend(memoryService.getMemoryTrend());
  };

  const updateData = () => {
    setSnapshots(memoryService.getSnapshots());
    setMemoryStats(memoryService.getMemoryStats());
    setMemoryTrend(memoryService.getMemoryTrend());
  };

  const handleSnapshotCreated = (snapshot: MemorySnapshot) => {
    setSnapshots(prev => [...prev, snapshot]);
  };

  const handleLeakDetected = (leak: MemoryLeakDetection) => {
    setLeakDetections(prev => [...prev, leak]);
  };

  const handleToggleMonitoring = () => {
    if (isMonitoring) {
      memoryService.stopMonitoring();
    } else {
      memoryService.startMonitoring();
    }
    setIsMonitoring(!isMonitoring);
  };

  const handleCreateSnapshot = () => {
    memoryService.createSnapshot();
  };

  const handleForceGC = () => {
    memoryService.forceGarbageCollection();
  };

  const handleClearSnapshots = () => {
    memoryService.clearSnapshots();
    setSnapshots([]);
  };

  const handleClearLeaks = () => {
    memoryService.clearLeakDetections();
    setLeakDetections([]);
  };

  const handleCompareSnapshots = () => {
    if (selectedSnapshots.length === 2) {
      // TODO: 实现快照比较功能
      console.log('Compare snapshots:', selectedSnapshots);
    }
  };

  const handleViewLeakDetail = (leak: MemoryLeakDetection) => {
    setSelectedLeak(leak);
    setLeakDetailVisible(true);
  };

  // 获取泄漏严重程度颜色
  const getLeakSeverityColor = (severity: LeakSeverity) => {
    switch (severity) {
      case LeakSeverity.LOW:
        return 'blue';
      case LeakSeverity.MEDIUM:
        return 'orange';
      case LeakSeverity.HIGH:
        return 'red';
      case LeakSeverity.CRITICAL:
        return 'red';
      default:
        return 'default';
    }
  };

  // 获取泄漏类型图标
  const getLeakTypeIcon = (type: LeakType) => {
    switch (type) {
      case LeakType.DETACHED_DOM:
        return <BugOutlined />;
      case LeakType.EVENT_LISTENERS:
        return <WarningOutlined />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  // 准备内存趋势图表数据
  const prepareMemoryTrendData = () => {
    if (!memoryTrend.timestamps || memoryTrend.timestamps.length === 0) {
      return [];
    }

    return memoryTrend.timestamps.map((timestamp: number, index: number) => ({
      time: new Date(timestamp).toLocaleTimeString(),
      used: Math.round(memoryTrend.usedMemory[index] / 1024 / 1024),
      total: Math.round(memoryTrend.totalMemory[index] / 1024 / 1024)
    }));
  };

  // 快照表格列定义
  const snapshotColumns = [
    {
      title: t('debug.memory.timestamp'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => new Date(timestamp).toLocaleString()
    },
    {
      title: t('debug.memory.usedMemory'),
      dataIndex: 'usedJSHeapSize',
      key: 'usedJSHeapSize',
      render: (size: number) => `${(size / 1024 / 1024).toFixed(2)} MB`
    },
    {
      title: t('debug.memory.totalMemory'),
      dataIndex: 'totalJSHeapSize',
      key: 'totalJSHeapSize',
      render: (size: number) => `${(size / 1024 / 1024).toFixed(2)} MB`
    },
    {
      title: t('debug.memory.objectCount'),
      dataIndex: 'objectCounts',
      key: 'objectCounts',
      render: (counts: any) => counts.total.toLocaleString()
    },
    {
      title: t('debug.memory.detachedNodes'),
      dataIndex: 'objectCounts',
      key: 'detachedNodes',
      render: (counts: any) => (
        <Badge count={counts.detached} showZero>
          <span>{counts.detached}</span>
        </Badge>
      )
    }
  ];

  // 渲染概览页面
  const renderOverview = () => (
    <div className="memory-overview">
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('debug.memory.currentUsage')}
              value={(memoryStats.current?.usedJSHeapSize || 0) / 1024 / 1024}
              suffix="MB"
              precision={2}
              prefix={<MoreOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('debug.memory.peakUsage')}
              value={memoryStats.peak / 1024 / 1024}
              suffix="MB"
              precision={2}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('debug.memory.averageUsage')}
              value={memoryStats.average / 1024 / 1024}
              suffix="MB"
              precision={2}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title={t('debug.memory.leakCount')}
              value={memoryStats.leakCount}
              valueStyle={{ 
                color: memoryStats.leakCount > 0 ? '#ff4d4f' : '#52c41a' 
              }}
              prefix={<BugOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={16}>
          <Card title={t('debug.memory.memoryTrend')} size="small">
            <Area
              data={prepareMemoryTrendData()}
              xField="time"
              yField="used"
              height={200}

            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title={t('debug.memory.memoryInfo')} size="small">
            {memoryStats.current && (
              <Descriptions column={1} size="small">
                <Descriptions.Item label={t('debug.memory.heapLimit')}>
                  {(memoryStats.current.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB
                </Descriptions.Item>
                <Descriptions.Item label={t('debug.memory.usagePercent')}>
                  <Progress
                    percent={Math.round((memoryStats.current.usedJSHeapSize / memoryStats.current.jsHeapSizeLimit) * 100)}
                    size="small"
                  />
                </Descriptions.Item>
                <Descriptions.Item label={t('debug.memory.trend')}>
                  <Tag color={
                    memoryTrend.trend === 'increasing' ? 'red' :
                    memoryTrend.trend === 'decreasing' ? 'green' : 'blue'
                  }>
                    {memoryTrend.trend}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );

  // 渲染快照页面
  const renderSnapshots = () => (
    <div className="memory-snapshots">
      <div className="snapshots-toolbar">
        <Space>
          <Button
            type="primary"
            icon={<MoreOutlined />}
            onClick={handleCreateSnapshot}
          >
            {t('debug.memory.createSnapshot')}
          </Button>
          <Button
            icon={<DeleteOutlined />}
            onClick={handleForceGC}
          >
            {t('debug.memory.forceGC')}
          </Button>
          <Button
            icon={<CompressOutlined />}
            onClick={handleCompareSnapshots}
            disabled={selectedSnapshots.length !== 2}
          >
            {t('debug.memory.compareSnapshots')}
          </Button>
          <Button
            danger
            icon={<ClearOutlined />}
            onClick={handleClearSnapshots}
          >
            {t('debug.memory.clearSnapshots')}
          </Button>
        </Space>
      </div>

      <Table
        columns={snapshotColumns}
        dataSource={snapshots}
        rowKey="id"
        pagination={{ pageSize: 10 }}
        size="small"
        rowSelection={{
          selectedRowKeys: selectedSnapshots,
          onChange: (selectedRowKeys) => setSelectedSnapshots(selectedRowKeys as string[]),
          type: 'checkbox'
        }}
      />
    </div>
  );

  // 渲染泄漏检测页面
  const renderLeakDetection = () => (
    <div className="memory-leaks">
      <div className="leaks-toolbar">
        <Space>
          <Button
            danger
            icon={<ClearOutlined />}
            onClick={handleClearLeaks}
            disabled={leakDetections.length === 0}
          >
            {t('debug.memory.clearLeaks')}
          </Button>
          <Text type="secondary">
            {t('debug.memory.totalLeaks', { count: leakDetections.length })}
          </Text>
        </Space>
      </div>

      <List
        dataSource={leakDetections.slice().reverse()}
        renderItem={(leak) => (
          <List.Item
            actions={[
              <Button
                type="link"
                onClick={() => handleViewLeakDetail(leak)}
              >
                {t('debug.memory.viewDetails')}
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={getLeakTypeIcon(leak.type)}
              title={
                <Space>
                  <Text strong>{leak.description}</Text>
                  <Tag color={getLeakSeverityColor(leak.severity)}>
                    {leak.severity}
                  </Tag>
                </Space>
              }
              description={
                <div>
                  <Text type="secondary">
                    Growth rate: {(leak.growthRate / 1024 / 1024).toFixed(2)} MB
                  </Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {new Date(leak.timestamp).toLocaleString()}
                  </Text>
                </div>
              }
            />
          </List.Item>
        )}
        locale={{ emptyText: t('debug.memory.noLeaksDetected') }}
      />
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <MoreOutlined />
          {t('debug.memory.memoryMonitor')}
          <Button
            type={isMonitoring ? 'primary' : 'default'}
            size="small"
            icon={isMonitoring ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={handleToggleMonitoring}
          >
            {isMonitoring ? t('debug.memory.stopMonitoring') : t('debug.memory.startMonitoring')}
          </Button>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
      className="memory-monitor-panel"
    >
      {leakDetections.length > 0 && (
        <Alert
          message={t('debug.memory.leaksDetected')}
          description={t('debug.memory.leaksDetectedDesc', { count: leakDetections.length })}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <Space>
              <MemoryOutlined />
              {t('debug.memory.overview')}
            </Space>
          }
          key="overview"
        >
          {renderOverview()}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <LineChartOutlined />
              {t('debug.memory.snapshots')} ({snapshots.length})
            </Space>
          }
          key="snapshots"
        >
          {renderSnapshots()}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <Badge count={leakDetections.length}>
                <BugOutlined />
              </Badge>
              {t('debug.memory.leakDetection')}
            </Space>
          }
          key="leaks"
        >
          {renderLeakDetection()}
        </TabPane>
      </Tabs>

      {/* 泄漏详情对话框 */}
      <Modal
        title={t('debug.memory.leakDetails')}
        open={leakDetailVisible}
        onCancel={() => setLeakDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setLeakDetailVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
      >
        {selectedLeak && (
          <div className="leak-details">
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label={t('debug.memory.type')}>
                <Space>
                  {getLeakTypeIcon(selectedLeak.type)}
                  {selectedLeak.type}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.memory.severity')}>
                <Tag color={getLeakSeverityColor(selectedLeak.severity)}>
                  {selectedLeak.severity}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.memory.description')}>
                {selectedLeak.description}
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.memory.growthRate')}>
                {(selectedLeak.growthRate / 1024 / 1024).toFixed(2)} MB
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.memory.affectedObjects')}>
                <Space wrap>
                  {selectedLeak.affectedObjects.map(obj => (
                    <Tag key={obj}>{obj}</Tag>
                  ))}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label={t('debug.memory.suggestions')}>
                <List
                  size="small"
                  dataSource={selectedLeak.suggestions}
                  renderItem={(suggestion) => (
                    <List.Item>
                      <Text>• {suggestion}</Text>
                    </List.Item>
                  )}
                />
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </Modal>
  );
};

export default MemoryMonitorPanel;
